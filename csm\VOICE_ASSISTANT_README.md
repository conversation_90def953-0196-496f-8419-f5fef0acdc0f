# 🎤 AI Voice Assistant - Real-time Voice Conversation

A real-time voice conversation AI assistant using CSM 1B speech model, Whisper for speech-to-text, and WebSocket for real-time communication.

## Features

- **Real-time voice conversation** using WebSockets
- **Speech-to-text** using OpenAI Whisper
- **Text-to-speech** using CSM 1B model
- **Web-based interface** with modern UI
- **Cross-platform support** (Windows, macOS, Linux)
- **Audio format conversion** (WebM to WAV)

## Quick Start

### Option 1: Automated Setup (Recommended)

```bash
cd csm
python setup_and_run.py
```

This script will:
- Check system requirements
- Install all dependencies
- Start the server automatically

### Option 2: Manual Setup

#### Prerequisites

1. **Python 3.8+** - [Download Python](https://python.org)
2. **Node.js 16+** - [Download Node.js](https://nodejs.org)
3. **ffmpeg** (optional but recommended) - [Download ffmpeg](https://ffmpeg.org)

#### Installation Steps

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Install Node.js dependencies:**
```bash
npm install
```

3. **Start the server:**
```bash
node server.js
```

4. **Open your browser:**
Navigate to `http://localhost:3000`

## Usage

1. **Click "Start Recording"** to begin speaking
2. **Speak clearly** into your microphone
3. **Click "Stop Recording"** when finished
4. **Wait for processing** - the AI will transcribe your speech and generate a response
5. **Listen to the response** in the audio player

## System Requirements

### Minimum Requirements
- Python 3.8+
- Node.js 16+
- 4GB RAM
- CPU with AVX support

### Recommended Requirements
- Python 3.9+
- Node.js 18+
- 8GB+ RAM
- NVIDIA GPU with CUDA support (for faster processing)
- ffmpeg installed

## Troubleshooting

### Common Issues

1. **"response.wav not found" error:**
   - Check that Python dependencies are installed correctly
   - Ensure the CSM model can be loaded
   - Check console logs for detailed error messages

2. **Audio recording not working:**
   - Grant microphone permissions in your browser
   - Check that your microphone is working
   - Try a different browser (Chrome/Firefox recommended)

3. **Slow processing:**
   - Install CUDA for GPU acceleration
   - Use a smaller Whisper model (change "base" to "tiny" in process_audio.py)
   - Ensure sufficient RAM is available

4. **ffmpeg not found:**
   - Install ffmpeg from https://ffmpeg.org
   - Add ffmpeg to your system PATH
   - The system will try to work without it, but may have audio format issues

### Debug Mode

To see detailed logs, check the browser console and server terminal output.

## Architecture

```
Browser (WebRTC) → WebSocket → Node.js Server → Python Script
                                     ↓
                              Audio Processing:
                              1. WebM → WAV conversion
                              2. Whisper (Speech-to-Text)
                              3. CSM 1B (Text-to-Speech)
                              4. Response sent back
```

## API Reference

### WebSocket Events

**Client → Server:**
- `audio_stream`: Send audio data (WebM blob)

**Server → Client:**
- `audio_response`: Receive processed audio (WAV buffer)
- `error`: Error message

## Development

### File Structure
```
csm/
├── server.js              # Node.js WebSocket server
├── process_audio.py       # Python audio processing
├── audio_converter.py     # Audio format conversion
├── generator.py           # CSM model interface
├── models.py             # Model definitions
├── watermarking.py       # Audio watermarking
├── setup_and_run.py      # Automated setup script
├── public/
│   └── index.html        # Web interface
├── requirements.txt      # Python dependencies
└── package.json         # Node.js dependencies
```

### Customization

**Change AI Response Logic:**
Edit the `ai_response` generation in `process_audio.py`:
```python
# Current: Simple echo
ai_response = f"You said: {user_text}. That's interesting!"

# Custom: Add your own logic
ai_response = generate_custom_response(user_text)
```

**Adjust Audio Quality:**
Modify parameters in `process_audio.py`:
```python
# Whisper model size (tiny, base, small, medium, large)
stt_model = whisper.load_model("base")

# Audio generation length
max_audio_length_ms=10000
```

## License

This project is licensed under the Apache 2.0 License.

## Credits

- **CSM 1B Model**: Sesame AI Labs
- **Whisper**: OpenAI
- **Socket.IO**: Real-time communication
- **Express.js**: Web server framework
