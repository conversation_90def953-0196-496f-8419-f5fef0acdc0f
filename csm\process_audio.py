import sys
import whisper
import torchaudio
from generator import load_csm_1b

def main(audio_path):
    # Load Whisper model
    stt_model = whisper.load_model("base")
    result = stt_model.transcribe(audio_path)
    user_text = result['text']
    print(f"User said: {user_text}")

    # Generate AI response (placeholder)
    ai_response = f"You said: {user_text}"

    # Load CSM-1B model
    generator = load_csm_1b(device="cuda")

    # Generate speech
    audio = generator.generate(
        text=ai_response,
        speaker=0,
        context=[],
        max_audio_length_ms=10000,
    )

    # Save the response audio
    torchaudio.save("response.wav", audio.unsqueeze(0).cpu(), generator.sample_rate)

if __name__ == "__main__":
    audio_path = sys.argv[1]
    main(audio_path)
