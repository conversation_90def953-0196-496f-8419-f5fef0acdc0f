import sys
import os
import traceback
import tempfile
import torch
import whisper
import torchaudio
from generator import load_csm_1b
from audio_converter import convert_audio_to_wav

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")

        # Check if input file exists
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Input audio file not found: {audio_path}")

        # Convert input audio to WAV if needed
        wav_path = audio_path
        if not audio_path.lower().endswith('.wav'):
            print("Converting input audio to WAV format...")
            with open(audio_path, 'rb') as f:
                audio_data = f.read()

            # Create temporary WAV file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
                wav_path = temp_wav.name

            if not convert_audio_to_wav(audio_data, wav_path):
                # Fallback: try to use the original file directly
                print("Audio conversion failed, trying original file...")
                wav_path = audio_path

        # Select device
        if torch.cuda.is_available():
            device = "cuda"
        elif torch.backends.mps.is_available():
            device = "mps"
        else:
            device = "cpu"
        print(f"Using device: {device}")

        # Load Whisper model
        print("Loading Whisper model...")
        stt_model = whisper.load_model("base")

        # Transcribe audio
        print("Transcribing audio...")
        result = stt_model.transcribe(wav_path)
        user_text = result['text'].strip()
        print(f"User said: {user_text}")

        # Clean up temporary WAV file if created
        if wav_path != audio_path:
            try:
                os.unlink(wav_path)
            except:
                pass

        if not user_text:
            user_text = "I didn't catch that. Could you please repeat?"

        # Generate AI response
        ai_response = f"You said: {user_text}. That's interesting!"
        print(f"AI response: {ai_response}")

        # Load CSM-1B model
        print("Loading CSM-1B model...")
        generator = load_csm_1b(device=device)

        # Generate speech
        print("Generating speech...")
        audio = generator.generate(
            text=ai_response,
            speaker=0,
            context=[],
            max_audio_length_ms=10000,
        )

        # Save the response audio
        print(f"Saving audio to: {output_path}")
        torchaudio.save(output_path, audio.unsqueeze(0).cpu(), generator.sample_rate)

        if os.path.exists(output_path):
            print(f"Successfully saved response audio: {output_path}")
        else:
            raise RuntimeError("Failed to save response audio file")

    except Exception as e:
        print(f"Error in process_audio.py: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio.py <input_audio_path> [output_audio_path]")
        sys.exit(1)

    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    main(audio_path, output_path)
