import sys
import os
import wave
import struct
import math
import subprocess
import tempfile
import json

def create_audio_response(output_path, message="Audio received"):
    """Create a simple audio response - a pleasant tone sequence"""
    sample_rate = 16000
    duration = 3.0

    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)

        # Create different melodies based on message content
        message_lower = message.lower()

        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            notes = [440, 523, 659, 784]  # A, C, E, G - happy greeting
            print("INFO: Playing happy greeting melody")
        elif any(word in message_lower for word in ["thank", "thanks"]):
            notes = [523, 659, 784, 1047]  # C, E, G, C - grateful
            print("INFO: Playing grateful melody")
        elif any(word in message_lower for word in ["bye", "goodbye", "see you"]):
            notes = [784, 659, 523, 440]  # G, E, C, A - farewell
            print("INFO: Playing farewell melody")
        elif any(word in message_lower for word in ["yes", "yeah", "yep"]):
            notes = [523, 659, 523, 659]  # C, E, C, E - affirmative
            print("INFO: Playing affirmative melody")
        elif any(word in message_lower for word in ["no", "nope"]):
            notes = [440, 392, 349, 330]  # A, G, F, E - negative
            print("INFO: Playing negative melody")
        elif any(word in message_lower for word in ["help", "assist"]):
            notes = [659, 784, 880, 1047]  # E, G, A, C - helpful
            print("INFO: Playing helpful melody")
        else:
            notes = [440, 523, 659, 523]  # A, C, E, C - default
            print("INFO: Playing default melody")

        note_duration = duration / len(notes)

        for note_freq in notes:
            for i in range(int(sample_rate * note_duration)):
                t = (i / sample_rate)
                # Create a note with fade in/out
                fade = min(t / 0.1, 1.0) * min((note_duration - t) / 0.1, 1.0)
                amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) * fade
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def convert_to_wav(input_path):
    """Convert audio file to WAV format using ffmpeg"""
    try:
        # Create temporary WAV file
        temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_wav.close()

        # Try ffmpeg conversion
        cmd = [
            'ffmpeg', '-i', input_path,
            '-acodec', 'pcm_s16le',
            '-ar', '16000',
            '-ac', '1',
            '-y',
            temp_wav.name
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0 and os.path.exists(temp_wav.name):
            print("SUCCESS: Audio converted to WAV format using ffmpeg")
            return temp_wav.name
        else:
            print("WARNING: ffmpeg conversion failed")
            try:
                os.unlink(temp_wav.name)
            except:
                pass
            return None

    except Exception as e:
        print(f"WARNING: ffmpeg conversion failed: {str(e)}")
        return None

def try_simple_speech_recognition(audio_path):
    """Try to use simple speech recognition without heavy dependencies"""
    try:
        import speech_recognition as sr
        print("INFO: SpeechRecognition library found, attempting transcription...")

        r = sr.Recognizer()
        wav_path = audio_path
        temp_file_created = False

        # Check if we need to convert the audio format
        if not audio_path.lower().endswith('.wav'):
            print("INFO: Converting audio to WAV format...")
            wav_path = convert_to_wav(audio_path)
            if wav_path:
                temp_file_created = True
            else:
                print("WARNING: Could not convert audio format")
                return None

        # Try to load the audio file
        try:
            with sr.AudioFile(wav_path) as source:
                # Adjust for ambient noise
                r.adjust_for_ambient_noise(source, duration=0.5)
                audio = r.record(source)

            # Try Google Speech Recognition (free, no API key needed)
            try:
                text = r.recognize_google(audio, language='en-US')
                if text:
                    print(f"SUCCESS: Google Speech Recognition: '{text}'")
                    return text
            except sr.UnknownValueError:
                print("WARNING: Could not understand the audio")
            except sr.RequestError as e:
                print(f"WARNING: Google Speech Recognition service error: {e}")

        except Exception as e:
            print(f"WARNING: Error processing audio file: {e}")
        finally:
            # Clean up temporary file
            if temp_file_created and wav_path:
                try:
                    os.unlink(wav_path)
                except:
                    pass

        return None

    except ImportError:
        print("INFO: SpeechRecognition library not installed")
        print("INFO: Install with: pip install speechrecognition")
        return None
    except Exception as e:
        print(f"WARNING: Speech recognition failed: {str(e)}")
        return None

def analyze_audio_simple(audio_path):
    """Simple audio analysis without speech recognition"""
    try:
        # Try to get basic audio info
        with wave.open(audio_path, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            duration = frames / sample_rate

            print(f"INFO: Audio duration: {duration:.2f} seconds")

            # Simple heuristics based on audio characteristics
            if duration < 1.0:
                return "short"
            elif duration > 5.0:
                return "long"
            else:
                return "medium"

    except Exception as e:
        print(f"WARNING: Could not analyze audio: {e}")
        return "unknown"

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")

        # Check if input file exists
        if not os.path.exists(audio_path):
            print(f"WARNING: Input audio file not found: {audio_path}")
            create_audio_response(output_path, "File not found")
            return True

        print("SUCCESS: Input audio file found")

        # Try speech recognition
        transcribed_text = try_simple_speech_recognition(audio_path)

        # If speech recognition failed, do simple audio analysis
        if not transcribed_text:
            audio_type = analyze_audio_simple(audio_path)
            print(f"INFO: Audio analysis result: {audio_type}")

            # Create response based on audio characteristics
            if audio_type == "short":
                response_message = "quick"
            elif audio_type == "long":
                response_message = "detailed"
            else:
                response_message = "generic"
        else:
            response_message = transcribed_text
            print(f"INFO: Creating response for: '{transcribed_text}'")

        # Create audio response
        create_audio_response(output_path, response_message)

        if os.path.exists(output_path):
            print(f"SUCCESS: Audio response saved to: {output_path}")
            return True
        else:
            print("ERROR: Failed to create audio response")
            return False

    except Exception as e:
        print(f"ERROR: {str(e)}")
        # Try to create a basic response even if something fails
        try:
            create_audio_response(output_path, "error")
            print("INFO: Created basic error response")
        except:
            print("CRITICAL: Could not create any audio response")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_windows.py <input_audio_path> [output_audio_path]")
        sys.exit(1)

    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"

    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
