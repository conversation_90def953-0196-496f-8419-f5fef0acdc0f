import sys
import os
import traceback
import tempfile
import subprocess

def convert_webm_to_wav_simple(input_path, output_path):
    """Convert WebM to WAV using ffmpeg if available"""
    try:
        cmd = [
            'ffmpeg', '-i', input_path,
            '-acodec', 'pcm_s16le',
            '-ar', '16000',
            '-ac', '1',
            '-y',
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(output_path):
            print("SUCCESS: Audio converted to WAV format")
            return True
        else:
            print("WARNING: ffmpeg conversion failed")
            return False
            
    except Exception as e:
        print(f"WARNING: Audio conversion failed: {str(e)}")
        return False

def transcribe_audio(audio_path):
    """Transcribe audio using Whisper"""
    try:
        import whisper
        print("INFO: Loading Whisper model...")
        
        # Use tiny model for speed, base for better accuracy
        model = whisper.load_model("base")
        
        print("INFO: Transcribing audio...")
        result = model.transcribe(audio_path)
        text = result['text'].strip()
        
        if text:
            print(f"SUCCESS: Transcribed: '{text}'")
            return text
        else:
            print("WARNING: No text transcribed")
            return None
            
    except Exception as e:
        print(f"ERROR: Whisper transcription failed: {str(e)}")
        return None

def generate_ai_response(user_text):
    """Generate intelligent AI response"""
    user_text_lower = user_text.lower()
    
    # Simple but intelligent response generation
    if any(word in user_text_lower for word in ["hello", "hi", "hey"]):
        return "Hello! It's great to hear from you. How can I help you today?"
    
    elif any(word in user_text_lower for word in ["how are you", "how's it going"]):
        return "I'm doing wonderful, thank you for asking! I'm here and ready to chat with you."
    
    elif any(word in user_text_lower for word in ["what's your name", "who are you"]):
        return "I'm your AI voice assistant, powered by advanced speech technology. I'm here to have conversations with you!"
    
    elif any(word in user_text_lower for word in ["thank", "thanks"]):
        return "You're very welcome! I'm happy I could help. Is there anything else you'd like to talk about?"
    
    elif any(word in user_text_lower for word in ["bye", "goodbye", "see you"]):
        return "Goodbye! It was lovely talking with you. Have a wonderful day!"
    
    elif any(word in user_text_lower for word in ["help", "assist"]):
        return "I'm here to help! I can have conversations with you using voice. Just speak to me and I'll respond with my voice."
    
    elif any(word in user_text_lower for word in ["weather", "temperature"]):
        return "I don't have access to current weather data, but I'd love to chat about other topics with you!"
    
    elif any(word in user_text_lower for word in ["time", "date"]):
        return "I don't have access to the current time, but I'm always here whenever you want to chat!"
    
    elif "?" in user_text:
        return f"That's an interesting question about '{user_text}'. I'd love to discuss that with you further!"
    
    else:
        return f"I heard you say '{user_text}'. That's fascinating! Tell me more about what you're thinking."

def generate_speech_with_csm(text, output_path):
    """Generate speech using CSM model"""
    try:
        import torch
        from generator import load_csm_1b
        
        print("INFO: Loading CSM model...")
        
        # Select device
        if torch.cuda.is_available():
            device = "cuda"
            print("INFO: Using CUDA GPU")
        else:
            device = "cpu"
            print("INFO: Using CPU (this may be slower)")
        
        # Load CSM model
        generator = load_csm_1b(device=device)
        
        print("INFO: Generating speech...")
        audio = generator.generate(
            text=text,
            speaker=0,
            context=[],
            max_audio_length_ms=15000,  # 15 seconds max
        )
        
        # Save the audio
        import torchaudio
        torchaudio.save(output_path, audio.unsqueeze(0).cpu(), generator.sample_rate)
        
        print("SUCCESS: Speech generated with CSM model")
        return True
        
    except Exception as e:
        print(f"ERROR: CSM speech generation failed: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def create_fallback_audio(text, output_path):
    """Create simple audio response as fallback"""
    import wave
    import struct
    import math
    
    print("INFO: Creating fallback audio response")
    
    sample_rate = 16000
    duration = 3.0
    
    # Create a pleasant melody that indicates the text was received
    notes = [440, 523, 659, 784, 659, 523]  # A pleasant sequence
    
    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(sample_rate)
        
        note_duration = duration / len(notes)
        
        for note_freq in notes:
            for i in range(int(sample_rate * note_duration)):
                t = (i / sample_rate)
                fade = min(t / 0.1, 1.0) * min((note_duration - t) / 0.1, 1.0)
                amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) * fade
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def main(audio_path, output_path):
    try:
        print(f"=== AI Voice Assistant Processing ===")
        print(f"Input: {audio_path}")
        print(f"Output: {output_path}")
        
        # Check if input file exists
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Input audio file not found: {audio_path}")
        
        # Convert audio to WAV if needed
        wav_path = audio_path
        temp_file_created = False
        
        if not audio_path.lower().endswith('.wav'):
            print("INFO: Converting audio to WAV format...")
            temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_wav.close()
            
            if convert_webm_to_wav_simple(audio_path, temp_wav.name):
                wav_path = temp_wav.name
                temp_file_created = True
            else:
                print("WARNING: Could not convert audio, using original file")
        
        # Step 1: Transcribe speech to text
        user_text = transcribe_audio(wav_path)
        
        # Clean up temporary file
        if temp_file_created:
            try:
                os.unlink(wav_path)
            except:
                pass
        
        if not user_text:
            user_text = "I couldn't understand what you said, but I'm here to chat!"
        
        # Step 2: Generate AI response
        ai_response = generate_ai_response(user_text)
        print(f"AI Response: {ai_response}")
        
        # Step 3: Generate speech from AI response
        speech_success = generate_speech_with_csm(ai_response, output_path)
        
        if not speech_success:
            print("INFO: CSM failed, creating fallback audio")
            create_fallback_audio(ai_response, output_path)
        
        if os.path.exists(output_path):
            print("SUCCESS: AI voice response generated!")
            return True
        else:
            raise RuntimeError("Failed to create audio response")
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        
        # Create fallback response
        try:
            create_fallback_audio("Sorry, I encountered an error", output_path)
            print("INFO: Created error fallback response")
        except:
            pass
        
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_full.py <input_audio_path> [output_audio_path]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    
    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
