@echo off
echo 🔧 Installing PyTorch CPU Version for Windows
echo =============================================

echo.
echo This will install PyTorch CPU version that works reliably on Windows
echo.

REM Uninstall existing PyTorch
echo 🗑️ Removing existing PyTorch installations...
python -m pip uninstall torch torchaudio torchvision -y

echo.
echo 📦 Installing PyTorch CPU version...
python -m pip install torch==2.4.0+cpu torchaudio==2.4.0+cpu --index-url https://download.pytorch.org/whl/cpu

echo.
echo 📦 Installing other required packages...
python -m pip install openai-whisper speechrecognition

echo.
echo 🧪 Testing PyTorch installation...
python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print('SUCCESS: PyTorch working!')"

if errorlevel 1 (
    echo.
    echo ❌ PyTorch installation failed. Please check the error messages above.
    pause
    exit /b 1
) else (
    echo.
    echo ✅ PyTorch installed successfully!
    echo.
    echo 🧪 Testing Whisper...
    python -c "import whisper; print('SUCCESS: Whisper working!')"
    
    if errorlevel 1 (
        echo ❌ Whisper test failed
    ) else (
        echo ✅ Whisper working!
    )
)

echo.
echo 🎉 Installation complete! You can now use the full AI voice assistant.
echo.
pause
