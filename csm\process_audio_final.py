import sys
import os
import wave
import struct
import math

def create_audio_response(output_path, message="Audio received"):
    """Create a simple audio response - a pleasant tone sequence"""
    sample_rate = 16000
    duration = 3.0
    
    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        # Create different melodies based on message content
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            notes = [440, 523, 659, 784]  # A, C, E, G - happy greeting
            print("INFO: Playing happy greeting melody")
        elif any(word in message_lower for word in ["thank", "thanks"]):
            notes = [523, 659, 784, 1047]  # C, E, G, C - grateful
            print("INFO: Playing grateful melody")
        elif any(word in message_lower for word in ["bye", "goodbye", "see you"]):
            notes = [784, 659, 523, 440]  # G, E, C, A - farewell
            print("INFO: Playing farewell melody")
        elif any(word in message_lower for word in ["yes", "yeah", "yep"]):
            notes = [523, 659, 523, 659]  # C, E, C, E - affirmative
            print("INFO: Playing affirmative melody")
        elif any(word in message_lower for word in ["no", "nope"]):
            notes = [440, 392, 349, 330]  # A, G, F, E - negative
            print("INFO: Playing negative melody")
        elif any(word in message_lower for word in ["help", "assist"]):
            notes = [659, 784, 880, 1047]  # E, G, A, C - helpful
            print("INFO: Playing helpful melody")
        else:
            notes = [440, 523, 659, 523]  # A, C, E, C - default
            print("INFO: Playing default melody")
        
        note_duration = duration / len(notes)
        
        for note_freq in notes:
            for i in range(int(sample_rate * note_duration)):
                t = (i / sample_rate)
                # Create a note with fade in/out
                fade = min(t / 0.1, 1.0) * min((note_duration - t) / 0.1, 1.0)
                amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) * fade
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def analyze_audio_characteristics(audio_path):
    """Analyze audio file characteristics to guess content"""
    try:
        file_size = os.path.getsize(audio_path)
        print(f"INFO: Audio file size: {file_size} bytes")
        
        # Simple heuristics based on file size and duration
        if file_size < 20000:  # Very small file
            return "short"
        elif file_size > 100000:  # Large file
            return "long"
        elif file_size > 50000:  # Medium-large file
            return "medium-long"
        else:  # Medium file
            return "medium"
            
    except Exception as e:
        print(f"WARNING: Could not analyze audio: {e}")
        return "unknown"

def guess_content_from_audio_size(audio_type):
    """Make educated guesses about content based on audio characteristics"""
    if audio_type == "short":
        # Short recordings might be greetings or quick responses
        return "hello"
    elif audio_type == "long":
        # Long recordings might be detailed questions or explanations
        return "help"
    elif audio_type == "medium-long":
        # Medium-long might be thanks or goodbyes
        return "thank"
    else:
        # Default for medium recordings
        return "generic"

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")
        
        # Check if input file exists
        if not os.path.exists(audio_path):
            print(f"WARNING: Input audio file not found: {audio_path}")
            create_audio_response(output_path, "File not found")
            return True
        
        print("SUCCESS: Input audio file found")
        
        # Since we can't easily do speech recognition without proper audio conversion,
        # let's use file characteristics to make intelligent guesses
        audio_type = analyze_audio_characteristics(audio_path)
        guessed_content = guess_content_from_audio_size(audio_type)
        
        print(f"INFO: Audio analysis result: {audio_type}")
        print(f"INFO: Guessed content type: {guessed_content}")
        
        # Create response based on our guess
        create_audio_response(output_path, guessed_content)
        
        if os.path.exists(output_path):
            print(f"SUCCESS: Audio response saved to: {output_path}")
            return True
        else:
            print("ERROR: Failed to create audio response")
            return False
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        # Try to create a basic response even if something fails
        try:
            create_audio_response(output_path, "error")
            print("INFO: Created basic error response")
        except:
            print("CRITICAL: Could not create any audio response")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_final.py <input_audio_path> [output_audio_path]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    
    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
