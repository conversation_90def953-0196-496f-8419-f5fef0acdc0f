#!/usr/bin/env python3
"""
Setup and run script for the AI Voice Assistant
This script helps install dependencies and start the server
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True if isinstance(cmd, str) else False, 
                              check=True, capture_output=False)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        return False
    except FileNotFoundError as e:
        print(f"Command not found: {e}")
        return False

def check_python():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_node():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js {result.stdout.strip()} detected")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js not found. Please install Node.js from https://nodejs.org/")
    return False

def check_ffmpeg():
    """Check if ffmpeg is installed"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ ffmpeg detected")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️  ffmpeg not found. Audio conversion may not work properly.")
    print("   Please install ffmpeg from https://ffmpeg.org/")
    return False

def install_python_deps():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      "Installing Python packages"):
        return False
    
    return True

def install_node_deps():
    """Install Node.js dependencies"""
    print("\n📦 Installing Node.js dependencies...")
    
    if not run_command(["npm", "install"], "Installing Node.js packages"):
        return False
    
    return True

def start_server():
    """Start the Node.js server"""
    print("\n🚀 Starting the AI Voice Assistant server...")
    print("The server will start on http://localhost:3000")
    print("Press Ctrl+C to stop the server")
    
    try:
        subprocess.run(["node", "server.js"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        return False
    
    return True

def main():
    """Main setup and run function"""
    print("🎤 AI Voice Assistant Setup and Run Script")
    print("=" * 50)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"Working directory: {script_dir}")
    
    # Check system requirements
    print("\n🔍 Checking system requirements...")
    
    if not check_python():
        sys.exit(1)
    
    node_ok = check_node()
    ffmpeg_ok = check_ffmpeg()
    
    if not node_ok:
        print("\n❌ Node.js is required but not found. Please install it first.")
        sys.exit(1)
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    
    if not install_python_deps():
        print("❌ Failed to install Python dependencies")
        sys.exit(1)
    
    if not install_node_deps():
        print("❌ Failed to install Node.js dependencies")
        sys.exit(1)
    
    print("\n✅ All dependencies installed successfully!")
    
    if not ffmpeg_ok:
        print("\n⚠️  Warning: ffmpeg is not installed.")
        print("   The system will try to work without it, but audio conversion may fail.")
        print("   For best results, please install ffmpeg.")
    
    # Start the server
    print("\n🎉 Setup complete! Starting the server...")
    start_server()

if __name__ == "__main__":
    main()
