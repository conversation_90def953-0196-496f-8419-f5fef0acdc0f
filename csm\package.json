{"name": "ai-voice-assistant", "version": "1.0.0", "description": "Real-time AI voice conversation assistant using CSM 1B and Whisper", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"No tests specified\" && exit 0", "setup": "npm install && python -m pip install -r requirements.txt", "test-setup": "python test_setup.py"}, "keywords": ["ai", "voice", "assistant", "speech", "websocket", "realtime", "conversation"], "author": "AI Voice Assistant", "license": "Apache-2.0", "dependencies": {"express": "^5.1.0", "fs": "^0.0.1-security", "socket.io": "^4.8.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}