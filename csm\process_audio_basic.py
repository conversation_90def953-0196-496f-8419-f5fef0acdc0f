import sys
import os
import wave
import struct
import math

def create_audio_response(output_path, message="Audio received"):
    """Create a simple audio response - a pleasant tone sequence"""
    sample_rate = 16000
    duration = 2.0
    
    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        # Create a pleasant melody
        notes = [440, 523, 659, 523]  # A, C, E, C
        note_duration = duration / len(notes)
        
        for note_freq in notes:
            for i in range(int(sample_rate * note_duration)):
                t = (i / sample_rate)
                # Create a note with fade in/out
                fade = min(t / 0.1, 1.0) * min((note_duration - t) / 0.1, 1.0)
                amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) * fade
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")
        
        # Check if input file exists
        if not os.path.exists(audio_path):
            print(f"WARNING: Input audio file not found: {audio_path}")
            print("INFO: Creating response anyway...")
        else:
            print("SUCCESS: Input audio file found")
        
        # For now, just create a simple audio response
        # In the future, this could be enhanced with speech recognition
        print("INFO: Creating audio response...")
        create_audio_response(output_path, "Hello, I heard your message!")
        
        if os.path.exists(output_path):
            print(f"SUCCESS: Audio response saved to: {output_path}")
            return True
        else:
            print("ERROR: Failed to create audio response")
            return False
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        # Try to create a basic response even if something fails
        try:
            create_audio_response(output_path, "Error occurred")
            print("INFO: Created basic error response")
        except:
            print("CRITICAL: Could not create any audio response")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_basic.py <input_audio_path> [output_audio_path]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    
    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
