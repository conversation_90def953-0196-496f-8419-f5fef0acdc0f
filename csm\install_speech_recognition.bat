@echo off
echo 🎤 Installing Speech Recognition for Voice Assistant
echo ===================================================

echo.
echo Installing SpeechRecognition library...
python -m pip install speechrecognition

echo.
echo Testing installation...
python -c "import speech_recognition as sr; print('SUCCESS: SpeechRecognition installed')"

if errorlevel 1 (
    echo.
    echo ❌ Installation failed. The system will work without speech recognition.
    echo You'll still hear musical responses based on your voice input.
) else (
    echo.
    echo ✅ Speech recognition installed successfully!
    echo The system will now try to understand your speech and respond with different melodies.
)

echo.
echo 🚀 Starting the enhanced voice assistant...
echo.
node server.js

pause
