#!/usr/bin/env python3
"""
Test script to verify the AI Voice Assistant setup
"""

import sys
import os
import traceback
import tempfile
import subprocess

def test_imports():
    """Test if all required Python packages can be imported"""
    print("🔍 Testing Python imports...")
    
    required_packages = [
        ('torch', 'PyTorch'),
        ('torchaudio', 'TorchAudio'),
        ('whisper', 'OpenAI Whisper'),
        ('transformers', 'Transformers'),
        ('huggingface_hub', 'Hugging Face Hub'),
    ]
    
    failed_imports = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {name}")
        except ImportError as e:
            print(f"  ❌ {name}: {e}")
            failed_imports.append(name)
    
    return len(failed_imports) == 0

def test_whisper():
    """Test Whisper model loading"""
    print("\n🎤 Testing Whisper model...")
    
    try:
        import whisper
        model = whisper.load_model("tiny")  # Use tiny model for faster testing
        print("  ✓ Whisper model loaded successfully")
        return True
    except Exception as e:
        print(f"  ❌ Whisper test failed: {e}")
        return False

def test_csm_imports():
    """Test CSM-related imports"""
    print("\n🗣️ Testing CSM imports...")
    
    try:
        from models import Model
        print("  ✓ Models module imported")
        
        from generator import load_csm_1b
        print("  ✓ Generator module imported")
        
        from watermarking import load_watermarker
        print("  ✓ Watermarking module imported")
        
        return True
    except Exception as e:
        print(f"  ❌ CSM import test failed: {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return False

def test_audio_converter():
    """Test audio converter"""
    print("\n🔄 Testing audio converter...")
    
    try:
        from audio_converter import convert_audio_to_wav
        print("  ✓ Audio converter imported")
        return True
    except Exception as e:
        print(f"  ❌ Audio converter test failed: {e}")
        return False

def test_ffmpeg():
    """Test ffmpeg availability"""
    print("\n🎵 Testing ffmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("  ✓ ffmpeg is available")
            return True
        else:
            print("  ❌ ffmpeg returned error")
            return False
    except FileNotFoundError:
        print("  ⚠️  ffmpeg not found (optional)")
        return False
    except subprocess.TimeoutExpired:
        print("  ❌ ffmpeg test timed out")
        return False
    except Exception as e:
        print(f"  ❌ ffmpeg test failed: {e}")
        return False

def test_device_availability():
    """Test available compute devices"""
    print("\n💻 Testing compute devices...")
    
    try:
        import torch
        
        print(f"  CPU: Available")
        
        if torch.cuda.is_available():
            print(f"  ✓ CUDA: Available (GPU count: {torch.cuda.device_count()})")
            for i in range(torch.cuda.device_count()):
                print(f"    - GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("  ⚠️  CUDA: Not available")
        
        if torch.backends.mps.is_available():
            print("  ✓ MPS (Apple Silicon): Available")
        else:
            print("  ⚠️  MPS: Not available")
        
        return True
    except Exception as e:
        print(f"  ❌ Device test failed: {e}")
        return False

def test_file_structure():
    """Test if required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'server.js',
        'process_audio.py',
        'generator.py',
        'models.py',
        'watermarking.py',
        'audio_converter.py',
        'requirements.txt',
        'package.json',
        'public/index.html'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ❌ {file_path} (missing)")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """Run all tests"""
    print("🧪 AI Voice Assistant Setup Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Imports", test_imports),
        ("Whisper", test_whisper),
        ("CSM Imports", test_csm_imports),
        ("Audio Converter", test_audio_converter),
        ("ffmpeg", test_ffmpeg),
        ("Compute Devices", test_device_availability),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup looks good.")
        print("You can now run: python setup_and_run.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
        print("Try running: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
