/*! https://mths.be/utf8js v2.1.2 by @mathias */
declare var stringFromCharCode: (...codes: number[]) => string;
declare function ucs2decode(string: any): any[];
declare function ucs2encode(array: any): string;
declare function checkScalarValue(codePoint: any, strict: any): boolean;
declare function createByte(codePoint: any, shift: any): string;
declare function encodeCodePoint(codePoint: any, strict: any): string;
declare function utf8encode(string: any, opts: any): string;
declare function readContinuationByte(): number;
declare function decodeSymbol(strict: any): any;
declare var byteArray: any;
declare var byteCount: any;
declare var byteIndex: any;
declare function utf8decode(byteString: any, opts: any): string;
