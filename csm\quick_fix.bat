@echo off
echo 🔧 Quick Fix for PyTorch DLL Error
echo ===================================

echo.
echo This will fix the PyTorch DLL error you're experiencing.
echo.

REM Set environment variable to use simple audio processing
echo Setting fallback mode...
set USE_SIMPLE_AUDIO=true
setx USE_SIMPLE_AUDIO "true"

echo.
echo ✅ Fallback mode enabled. The system will now use simple audio processing.
echo.

REM Try to reinstall PyTorch with CPU version
echo 📦 Reinstalling PyTorch (CPU version)...
python -m pip uninstall torch torchaudio -y
python -m pip install torch==2.4.0+cpu torchaudio==2.4.0+cpu --index-url https://download.pytorch.org/whl/cpu

echo.
echo 🚀 Starting the voice assistant with fallback mode...
echo.
node server.js

pause
