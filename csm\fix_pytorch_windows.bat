@echo off
echo 🔧 Complete PyTorch Windows Fix
echo ===============================

echo.
echo This script will fix the PyTorch DLL error by:
echo 1. Installing Visual C++ Redistributable
echo 2. Completely removing and reinstalling PyTorch
echo 3. Installing alternative packages if needed
echo.

pause

REM Step 1: Download and install Visual C++ Redistributable
echo 📦 Step 1: Installing Visual C++ Redistributable...
echo.

REM Check if we can download the redistributable
echo Downloading Visual C++ Redistributable...
powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile 'vc_redist.x64.exe'}"

if exist vc_redist.x64.exe (
    echo Installing Visual C++ Redistributable...
    vc_redist.x64.exe /quiet /norestart
    echo ✅ Visual C++ Redistributable installed
    del vc_redist.x64.exe
) else (
    echo ❌ Could not download redistributable. Please install manually from:
    echo https://aka.ms/vs/17/release/vc_redist.x64.exe
    echo.
    echo Press any key after installing it manually...
    pause
)

echo.
echo 📦 Step 2: Completely removing PyTorch...
python -m pip uninstall torch torchaudio torchvision torchtext -y
python -m pip cache purge

echo.
echo 📦 Step 3: Installing PyTorch CPU version...
python -m pip install torch==2.4.0+cpu torchaudio==2.4.0+cpu --index-url https://download.pytorch.org/whl/cpu --force-reinstall --no-cache-dir

echo.
echo 🧪 Testing PyTorch...
python -c "import torch; print('SUCCESS: PyTorch working!')" 2>nul

if errorlevel 1 (
    echo.
    echo ❌ PyTorch still not working. Trying alternative approach...
    echo.
    echo 📦 Installing older PyTorch version...
    python -m pip uninstall torch torchaudio -y
    python -m pip install torch==2.0.1+cpu torchaudio==2.0.2+cpu --index-url https://download.pytorch.org/whl/cpu --force-reinstall --no-cache-dir
    
    echo 🧪 Testing older PyTorch...
    python -c "import torch; print('SUCCESS: Older PyTorch working!')" 2>nul
    
    if errorlevel 1 (
        echo.
        echo ❌ PyTorch installation failed completely.
        echo.
        echo 🔄 Creating PyTorch-free version of the voice assistant...
        echo This version will work without PyTorch but with limited functionality.
        echo.
        goto :create_no_pytorch_version
    ) else (
        echo ✅ Older PyTorch version working!
        goto :install_other_packages
    )
) else (
    echo ✅ PyTorch working!
    goto :install_other_packages
)

:install_other_packages
echo.
echo 📦 Installing other packages...
python -m pip install openai-whisper speechrecognition

echo.
echo 🧪 Testing Whisper...
python -c "import whisper; print('SUCCESS: Whisper working!')" 2>nul

if errorlevel 1 (
    echo ❌ Whisper failed, but PyTorch is working
    echo The system will work with basic speech recognition
) else (
    echo ✅ Whisper working!
)

echo.
echo 🎉 Setup complete! Starting the AI voice assistant...
echo.
node server.js
goto :end

:create_no_pytorch_version
echo Creating PyTorch-free version...

REM Create a version that doesn't use PyTorch at all
echo import sys > process_audio_no_pytorch.py
echo import os >> process_audio_no_pytorch.py
echo import wave >> process_audio_no_pytorch.py
echo import struct >> process_audio_no_pytorch.py
echo import math >> process_audio_no_pytorch.py
echo. >> process_audio_no_pytorch.py
echo def create_response_audio(output_path, message=""): >> process_audio_no_pytorch.py
echo     """Create audio response without PyTorch""" >> process_audio_no_pytorch.py
echo     sample_rate = 16000 >> process_audio_no_pytorch.py
echo     duration = 3.0 >> process_audio_no_pytorch.py
echo     with wave.open(output_path, 'w') as wav_file: >> process_audio_no_pytorch.py
echo         wav_file.setnchannels(1) >> process_audio_no_pytorch.py
echo         wav_file.setsampwidth(2) >> process_audio_no_pytorch.py
echo         wav_file.setframerate(sample_rate) >> process_audio_no_pytorch.py
echo         notes = [440, 523, 659, 523] >> process_audio_no_pytorch.py
echo         note_duration = duration / len(notes) >> process_audio_no_pytorch.py
echo         for note_freq in notes: >> process_audio_no_pytorch.py
echo             for i in range(int(sample_rate * note_duration)): >> process_audio_no_pytorch.py
echo                 t = i / sample_rate >> process_audio_no_pytorch.py
echo                 amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) >> process_audio_no_pytorch.py
echo                 sample = int(amplitude * 32767) >> process_audio_no_pytorch.py
echo                 wav_file.writeframes(struct.pack('<h', sample)) >> process_audio_no_pytorch.py
echo. >> process_audio_no_pytorch.py
echo if __name__ == "__main__": >> process_audio_no_pytorch.py
echo     create_response_audio(sys.argv[2] if len(sys.argv) ^> 2 else "response.wav") >> process_audio_no_pytorch.py
echo     print("SUCCESS: Audio response created") >> process_audio_no_pytorch.py

echo.
echo ✅ Created PyTorch-free version
echo This version will create musical responses without AI speech
echo.
echo Starting the basic voice assistant...
node server.js

:end
pause
