const express = require("express");
const http = require("http");
const socketIo = require("socket.io");
const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

app.use(express.static("public"));

io.on("connection", (socket) => {
  console.log("Client connected");

  socket.on("audio_stream", (audioChunk) => {
    const inputPath = path.join(__dirname, "input.wav");
    const outputPath = path.join(__dirname, "response.wav");

    // Save the audio chunk to a file
    fs.writeFileSync(inputPath, audioChunk);

    // Call the Python script
    const python = spawn("python3", ["process_audio.py", inputPath]);

    python.on("close", (code) => {
      // Check if response.wav exists
      if (fs.existsSync(outputPath)) {
        const responseAudio = fs.readFileSync(outputPath);
        socket.emit("audio_response", responseAudio);
      } else {
        console.error("response.wav not found.");
        socket.emit("error", "Processing failed: response.wav not found.");
      }
    });
  });

  socket.on("disconnect", () => {
    console.log("Client disconnected");
  });
});

server.listen(3000, () => {
  console.log("Server is running on port 3000");
});
