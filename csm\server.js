const express = require("express");
const http = require("http");
const socketIo = require("socket.io");
const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");
const os = require("os");

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

app.use(express.static("public"));

// Function to determine Python command
function getPythonCommand() {
  const platform = os.platform();
  if (platform === "win32") {
    return "python"; // Windows typically uses 'python'
  } else {
    return "python3"; // Unix-like systems use 'python3'
  }
}

io.on("connection", (socket) => {
  console.log("Client connected");

  socket.on("audio_stream", (audioChunk) => {
    console.log("Received audio chunk, size:", audioChunk.length);

    const timestamp = Date.now();
    const inputPath = path.join(__dirname, `input_${timestamp}.wav`);
    const outputPath = path.join(__dirname, `response_${timestamp}.wav`);

    try {
      // Save the audio chunk to a file
      fs.writeFileSync(inputPath, audioChunk);
      console.log("Saved input audio to:", inputPath);

      // Call the Python script with proper error handling
      const pythonCmd = getPythonCommand();

      // Use full AI voice assistant with CSM
      let scriptToRun = "process_audio_full.py";
      console.log("Using full AI voice assistant with CSM speech generation");

      console.log("Executing:", pythonCmd, scriptToRun, inputPath, outputPath);

      const python = spawn(pythonCmd, [scriptToRun, inputPath, outputPath], {
        cwd: __dirname,
        stdio: ["pipe", "pipe", "pipe"],
      });

      let stdout = "";
      let stderr = "";

      python.stdout.on("data", (data) => {
        stdout += data.toString();
        console.log("Python stdout:", data.toString());
      });

      python.stderr.on("data", (data) => {
        stderr += data.toString();
        console.error("Python stderr:", data.toString());
      });

      python.on("close", (code) => {
        console.log(`Python process exited with code: ${code}`);

        if (code === 0) {
          // Check if response.wav exists
          if (fs.existsSync(outputPath)) {
            try {
              const responseAudio = fs.readFileSync(outputPath);
              console.log(
                "Sending response audio, size:",
                responseAudio.length
              );
              socket.emit("audio_response", responseAudio);

              // Clean up files
              setTimeout(() => {
                try {
                  if (fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
                  if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
                } catch (cleanupError) {
                  console.error("Cleanup error:", cleanupError);
                }
              }, 1000);
            } catch (readError) {
              console.error("Error reading response file:", readError);
              socket.emit("error", "Failed to read response audio file.");
            }
          } else {
            console.error("response.wav not found at:", outputPath);
            socket.emit("error", "Processing failed: response.wav not found.");
          }
        } else {
          console.error("Python script failed with code:", code);
          console.error("stderr:", stderr);
          socket.emit(
            "error",
            `Processing failed with code ${code}: ${stderr}`
          );
        }
      });

      python.on("error", (error) => {
        console.error("Failed to start Python process:", error);
        socket.emit("error", "Failed to start audio processing.");
      });
    } catch (error) {
      console.error("Error processing audio:", error);
      socket.emit("error", "Server error processing audio.");
    }
  });

  socket.on("disconnect", () => {
    console.log("Client disconnected");
  });
});

server.listen(3000, () => {
  console.log("Server is running on port 3000");
  console.log("Open http://localhost:3000 in your browser");
});
