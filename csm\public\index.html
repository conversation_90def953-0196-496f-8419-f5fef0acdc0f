<!DOCTYPE html>
<html>
<head>
  <title>AI Voice Assistant</title>
</head>
<body>
  <h1>Speak to the AI Assistant</h1>
  <button id="start">Start</button>
  <button id="stop">Stop</button>
  <audio id="response" controls></audio>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    const socket = io();
    const startButton = document.getElementById('start');
    const stopButton = document.getElementById('stop');
    const responseAudio = document.getElementById('response');

    let mediaRecorder;
    let audioChunks = [];

    startButton.addEventListener('click', async () => {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorder = new MediaRecorder(stream);
      mediaRecorder.start();

      mediaRecorder.ondataavailable = (event) => {
        socket.emit('audio_stream', event.data);
      };
    });

    stopButton.addEventListener('click', () => {
      mediaRecorder.stop();
    });

    socket.on('audio_response', (audioBuffer) => {
      const blob = new Blob([audioBuffer], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(blob);
      responseAudio.src = audioUrl;
    });
  </script>
</body>
</html>
