<!DOCTYPE html>
<html>
<head>
  <title>AI Voice Assistant</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .controls {
      text-align: center;
      margin: 20px 0;
    }
    button {
      padding: 15px 30px;
      margin: 10px;
      font-size: 16px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    #start {
      background-color: #4CAF50;
      color: white;
    }
    #start:hover {
      background-color: #45a049;
    }
    #start:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    #stop {
      background-color: #f44336;
      color: white;
    }
    #stop:hover {
      background-color: #da190b;
    }
    #stop:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .status {
      text-align: center;
      margin: 20px 0;
      padding: 10px;
      border-radius: 5px;
      font-weight: bold;
    }
    .status.recording {
      background-color: #ffebee;
      color: #c62828;
    }
    .status.processing {
      background-color: #fff3e0;
      color: #ef6c00;
    }
    .status.ready {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    .status.error {
      background-color: #ffebee;
      color: #c62828;
    }
    audio {
      width: 100%;
      margin: 20px 0;
    }
    .instructions {
      background-color: #e3f2fd;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎤 AI Voice Assistant</h1>

    <div class="instructions">
      <h3>Instructions:</h3>
      <ol>
        <li>Click "Start Recording" to begin speaking</li>
        <li>Speak clearly into your microphone</li>
        <li>Click "Stop Recording" when you're done</li>
        <li>Wait for the AI to process and respond</li>
        <li>Listen to the response in the audio player below</li>
      </ol>
    </div>

    <div class="controls">
      <button id="start">🎤 Start Recording</button>
      <button id="stop" disabled>⏹️ Stop Recording</button>
    </div>

    <div id="status" class="status ready">Ready to record</div>

    <audio id="response" controls style="display: none;"></audio>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    const socket = io();
    const startButton = document.getElementById('start');
    const stopButton = document.getElementById('stop');
    const responseAudio = document.getElementById('response');
    const statusDiv = document.getElementById('status');

    let mediaRecorder;
    let audioChunks = [];
    let isRecording = false;

    function updateStatus(message, className) {
      statusDiv.textContent = message;
      statusDiv.className = `status ${className}`;
    }

    function setButtonStates(startEnabled, stopEnabled) {
      startButton.disabled = !startEnabled;
      stopButton.disabled = !stopEnabled;
    }

    startButton.addEventListener('click', async () => {
      try {
        audioChunks = [];
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true
          }
        });

        mediaRecorder = new MediaRecorder(stream, {
          mimeType: 'audio/webm;codecs=opus'
        });

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunks.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
          updateStatus('Processing your speech...', 'processing');
          socket.emit('audio_stream', audioBlob);

          // Stop all tracks to release microphone
          stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.start();
        isRecording = true;
        updateStatus('🔴 Recording... Click stop when finished', 'recording');
        setButtonStates(false, true);

      } catch (error) {
        console.error('Error accessing microphone:', error);
        updateStatus('Error: Could not access microphone', 'error');
      }
    });

    stopButton.addEventListener('click', () => {
      if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        setButtonStates(false, false);
      }
    });

    socket.on('audio_response', (audioBuffer) => {
      try {
        const blob = new Blob([audioBuffer], { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(blob);
        responseAudio.src = audioUrl;
        responseAudio.style.display = 'block';
        responseAudio.play();
        updateStatus('✅ Response received! Playing audio...', 'ready');
        setButtonStates(true, false);
      } catch (error) {
        console.error('Error playing response:', error);
        updateStatus('Error playing response audio', 'error');
        setButtonStates(true, false);
      }
    });

    socket.on('error', (errorMessage) => {
      console.error('Server error:', errorMessage);
      updateStatus(`Error: ${errorMessage}`, 'error');
      setButtonStates(true, false);
    });

    socket.on('connect', () => {
      updateStatus('Connected to server - Ready to record', 'ready');
    });

    socket.on('disconnect', () => {
      updateStatus('Disconnected from server', 'error');
      setButtonStates(false, false);
    });
  </script>
</body>
</html>
