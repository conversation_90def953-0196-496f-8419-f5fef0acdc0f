import sys
import os
import wave
import struct
import math
import subprocess
import tempfile

def create_speech_like_audio(text, output_path):
    """Create speech-like audio patterns based on text content"""
    sample_rate = 16000
    
    # Analyze text to determine response characteristics
    text_lower = text.lower()
    word_count = len(text.split())
    
    # Different audio patterns for different types of responses
    if any(word in text_lower for word in ["hello", "hi", "hey"]):
        # Happy greeting - upward melody
        notes = [440, 523, 659, 784]
        duration = 2.5
        pattern = "greeting"
    elif any(word in text_lower for word in ["thank", "welcome"]):
        # Grateful - warm tones
        notes = [523, 659, 784, 1047]
        duration = 2.0
        pattern = "grateful"
    elif any(word in text_lower for word in ["bye", "goodbye"]):
        # Farewell - descending
        notes = [784, 659, 523, 440]
        duration = 2.5
        pattern = "farewell"
    elif any(word in text_lower for word in ["help", "assist"]):
        # Helpful - ascending
        notes = [330, 440, 523, 659, 784]
        duration = 3.0
        pattern = "helpful"
    elif "?" in text:
        # Question - curious tone
        notes = [440, 523, 659, 523, 659]
        duration = 2.5
        pattern = "question"
    else:
        # Default conversational
        base_duration = 1.5 + (word_count * 0.2)  # Longer for more words
        duration = min(base_duration, 4.0)  # Cap at 4 seconds
        notes = [440, 523, 659, 523, 440]
        pattern = "conversation"
    
    print(f"INFO: Creating {pattern} audio pattern for: '{text[:50]}...'")
    print(f"INFO: Duration: {duration:.1f}s, Word count: {word_count}")
    
    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        note_duration = duration / len(notes)
        
        for i, note_freq in enumerate(notes):
            # Add slight variations to make it more speech-like
            freq_variation = note_freq * (1 + 0.05 * math.sin(i * 2))
            
            for j in range(int(sample_rate * note_duration)):
                t = j / sample_rate
                
                # Create speech-like envelope
                attack = min(t / 0.05, 1.0)  # Quick attack
                decay = min((note_duration - t) / 0.1, 1.0)  # Gradual decay
                envelope = attack * decay
                
                # Add some harmonics for richer sound
                fundamental = math.sin(2 * math.pi * freq_variation * t)
                harmonic2 = 0.3 * math.sin(2 * math.pi * freq_variation * 2 * t)
                harmonic3 = 0.1 * math.sin(2 * math.pi * freq_variation * 3 * t)
                
                amplitude = 0.3 * (fundamental + harmonic2 + harmonic3) * envelope
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def try_simple_transcription(audio_path):
    """Try to transcribe audio using available tools"""
    
    # Method 1: Try SpeechRecognition library
    try:
        import speech_recognition as sr
        print("INFO: Attempting transcription with SpeechRecognition...")
        
        r = sr.Recognizer()
        
        # Convert to WAV if needed
        wav_path = audio_path
        if not audio_path.lower().endswith('.wav'):
            wav_path = convert_to_wav(audio_path)
            if not wav_path:
                print("WARNING: Could not convert audio format")
                return None
        
        try:
            with sr.AudioFile(wav_path) as source:
                r.adjust_for_ambient_noise(source, duration=0.5)
                audio = r.record(source)
            
            # Try Google Speech Recognition
            text = r.recognize_google(audio, language='en-US')
            if text:
                print(f"SUCCESS: Transcribed: '{text}'")
                return text
                
        except sr.UnknownValueError:
            print("WARNING: Could not understand the audio")
        except sr.RequestError as e:
            print(f"WARNING: Speech recognition service error: {e}")
        except Exception as e:
            print(f"WARNING: Transcription error: {e}")
            
    except ImportError:
        print("INFO: SpeechRecognition not available")
    
    return None

def convert_to_wav(input_path):
    """Convert audio to WAV using ffmpeg"""
    try:
        temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_wav.close()
        
        cmd = [
            'ffmpeg', '-i', input_path,
            '-acodec', 'pcm_s16le',
            '-ar', '16000',
            '-ac', '1',
            '-y',
            temp_wav.name
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("SUCCESS: Audio converted to WAV")
            return temp_wav.name
        else:
            print("WARNING: Audio conversion failed")
            os.unlink(temp_wav.name)
            return None
            
    except Exception as e:
        print(f"WARNING: Conversion error: {e}")
        return None

def generate_response_text(user_text):
    """Generate intelligent response text"""
    if not user_text:
        return "I heard your voice but couldn't understand the words. Could you try speaking a bit clearer?"
    
    user_lower = user_text.lower()
    
    responses = {
        "hello": "Hello there! It's wonderful to hear your voice. How are you doing today?",
        "hi": "Hi! Great to connect with you. What would you like to talk about?",
        "hey": "Hey! Nice to hear from you. How can I help you today?",
        "how are you": "I'm doing fantastic, thank you for asking! I love having conversations. How are you feeling?",
        "what's your name": "I'm your AI voice assistant! I'm here to chat and help you with whatever you need.",
        "who are you": "I'm an AI assistant that can understand your speech and respond back to you. Pretty cool, right?",
        "thank you": "You're absolutely welcome! I'm so happy I could help. Is there anything else you'd like to discuss?",
        "thanks": "My pleasure! I really enjoy our conversations. What else is on your mind?",
        "help": "I'm here to help! I can have voice conversations with you. Just speak naturally and I'll respond.",
        "goodbye": "Goodbye! It's been lovely talking with you. Have a wonderful rest of your day!",
        "bye": "Bye for now! Thanks for the great conversation. Hope to chat again soon!",
    }
    
    # Check for exact matches first
    for key, response in responses.items():
        if key in user_lower:
            return response
    
    # Handle questions
    if "?" in user_text:
        return f"That's a great question about '{user_text}'. I'd love to explore that topic with you further!"
    
    # Default conversational response
    return f"I heard you say '{user_text}'. That's really interesting! Tell me more about what you're thinking."

def main(audio_path, output_path):
    try:
        print("=== AI Voice Assistant (PyTorch-Free Version) ===")
        print(f"Input: {audio_path}")
        print(f"Output: {output_path}")
        
        if not os.path.exists(audio_path):
            print("WARNING: Input file not found")
            create_speech_like_audio("I didn't receive your audio properly", output_path)
            return True
        
        # Try to transcribe the audio
        user_text = try_simple_transcription(audio_path)
        
        # Generate response text
        response_text = generate_response_text(user_text)
        print(f"Response: {response_text}")
        
        # Create speech-like audio response
        create_speech_like_audio(response_text, output_path)
        
        if os.path.exists(output_path):
            print("SUCCESS: Voice response created!")
            return True
        else:
            print("ERROR: Failed to create response")
            return False
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        try:
            create_speech_like_audio("Sorry, I encountered an error", output_path)
        except:
            pass
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_no_pytorch.py <input> [output]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    
    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
