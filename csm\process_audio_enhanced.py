import sys
import os
import wave
import struct
import math
import subprocess
import tempfile

def create_audio_response(output_path, message="Audio received"):
    """Create a simple audio response - a pleasant tone sequence"""
    sample_rate = 16000
    duration = 3.0
    
    with wave.open(output_path, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        # Create different melodies based on message content
        if "hello" in message.lower():
            notes = [440, 523, 659, 784]  # A, C, E, G - happy greeting
        elif "thank" in message.lower():
            notes = [523, 659, 784, 1047]  # C, E, G, C - grateful
        elif "bye" in message.lower() or "goodbye" in message.lower():
            notes = [784, 659, 523, 440]  # G, E, C, A - farewell
        else:
            notes = [440, 523, 659, 523]  # A, C, E, C - default
        
        note_duration = duration / len(notes)
        
        for note_freq in notes:
            for i in range(int(sample_rate * note_duration)):
                t = (i / sample_rate)
                # Create a note with fade in/out
                fade = min(t / 0.1, 1.0) * min((note_duration - t) / 0.1, 1.0)
                amplitude = 0.3 * math.sin(2 * math.pi * note_freq * t) * fade
                sample = int(amplitude * 32767)
                wav_file.writeframes(struct.pack('<h', sample))

def try_whisper_transcription(audio_path):
    """Try to use Whisper for speech recognition"""
    try:
        import whisper
        print("INFO: Whisper found, attempting transcription...")
        
        # Load the smallest model for speed
        model = whisper.load_model("tiny")
        result = model.transcribe(audio_path)
        text = result['text'].strip()
        
        if text:
            print(f"SUCCESS: Transcribed text: '{text}'")
            return text
        else:
            print("WARNING: No text transcribed")
            return None
            
    except ImportError:
        print("INFO: Whisper not available, skipping transcription")
        return None
    except Exception as e:
        print(f"WARNING: Whisper transcription failed: {str(e)}")
        return None

def try_speech_recognition(audio_path):
    """Try alternative speech recognition methods"""
    try:
        import speech_recognition as sr
        print("INFO: SpeechRecognition library found, attempting transcription...")
        
        r = sr.Recognizer()
        
        # Convert to WAV if needed and load
        with sr.AudioFile(audio_path) as source:
            audio = r.record(source)
        
        # Try Google Speech Recognition (free)
        try:
            text = r.recognize_google(audio)
            print(f"SUCCESS: Google Speech Recognition: '{text}'")
            return text
        except sr.UnknownValueError:
            print("WARNING: Google Speech Recognition could not understand audio")
        except sr.RequestError as e:
            print(f"WARNING: Google Speech Recognition error: {e}")
        
        return None
        
    except ImportError:
        print("INFO: SpeechRecognition library not available")
        return None
    except Exception as e:
        print(f"WARNING: Speech recognition failed: {str(e)}")
        return None

def convert_audio_format(input_path):
    """Try to convert audio to WAV format using ffmpeg"""
    try:
        # Create temporary WAV file
        temp_wav = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_wav.close()
        
        # Try ffmpeg conversion
        cmd = [
            'ffmpeg', '-i', input_path,
            '-acodec', 'pcm_s16le',
            '-ar', '16000',
            '-ac', '1',
            '-y',
            temp_wav.name
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(temp_wav.name):
            print("SUCCESS: Audio converted to WAV format")
            return temp_wav.name
        else:
            print("WARNING: ffmpeg conversion failed")
            os.unlink(temp_wav.name)
            return input_path
            
    except Exception as e:
        print(f"WARNING: Audio conversion failed: {str(e)}")
        return input_path

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")
        
        # Check if input file exists
        if not os.path.exists(audio_path):
            print(f"WARNING: Input audio file not found: {audio_path}")
            create_audio_response(output_path, "File not found")
            return True
        
        print("SUCCESS: Input audio file found")
        
        # Try to convert audio format if needed
        wav_path = audio_path
        if not audio_path.lower().endswith('.wav'):
            print("INFO: Converting audio format...")
            wav_path = convert_audio_format(audio_path)
        
        # Try speech recognition
        transcribed_text = None
        
        # Method 1: Try Whisper
        transcribed_text = try_whisper_transcription(wav_path)
        
        # Method 2: Try SpeechRecognition library if Whisper failed
        if not transcribed_text:
            transcribed_text = try_speech_recognition(wav_path)
        
        # Clean up temporary file if created
        if wav_path != audio_path:
            try:
                os.unlink(wav_path)
            except:
                pass
        
        # Generate response based on transcription
        if transcribed_text:
            response_message = f"I heard: {transcribed_text}"
            print(f"INFO: Creating response for: '{transcribed_text}'")
        else:
            response_message = "I heard your voice but couldn't understand the words"
            print("INFO: No transcription available, creating generic response")
        
        # Create audio response
        create_audio_response(output_path, transcribed_text or "generic")
        
        if os.path.exists(output_path):
            print(f"SUCCESS: Audio response saved to: {output_path}")
            return True
        else:
            print("ERROR: Failed to create audio response")
            return False
            
    except Exception as e:
        print(f"ERROR: {str(e)}")
        # Try to create a basic response even if something fails
        try:
            create_audio_response(output_path, "error")
            print("INFO: Created basic error response")
        except:
            print("CRITICAL: Could not create any audio response")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_enhanced.py <input_audio_path> [output_audio_path]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    
    success = main(audio_path, output_path)
    sys.exit(0 if success else 1)
