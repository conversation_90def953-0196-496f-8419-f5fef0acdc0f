#!/usr/bin/env python3
"""
Windows-specific PyTorch fix script
This script helps resolve common PyTorch DLL issues on Windows
"""

import os
import sys
import subprocess
import platform
import urllib.request
import tempfile
from pathlib import Path

def check_windows_version():
    """Check Windows version compatibility"""
    if platform.system() != "Windows":
        print("This script is for Windows only.")
        return False
    
    version = platform.version()
    print(f"Windows version: {version}")
    return True

def install_visual_cpp_redistributable():
    """Install Visual C++ Redistributable"""
    print("\n📦 Installing Visual C++ Redistributable...")
    print("This is required for PyTorch to work properly on Windows.")
    
    # URLs for Visual C++ Redistributable
    vc_redist_urls = {
        "x64": "https://aka.ms/vs/17/release/vc_redist.x64.exe",
        "x86": "https://aka.ms/vs/17/release/vc_redist.x86.exe"
    }
    
    # Detect architecture
    arch = "x64" if platform.machine().endswith('64') else "x86"
    url = vc_redist_urls[arch]
    
    print(f"Downloading Visual C++ Redistributable for {arch}...")
    
    try:
        with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as temp_file:
            urllib.request.urlretrieve(url, temp_file.name)
            installer_path = temp_file.name
        
        print("Running installer... (you may need to approve UAC prompt)")
        result = subprocess.run([installer_path, '/quiet', '/norestart'], 
                              capture_output=True, text=True)
        
        # Clean up
        try:
            os.unlink(installer_path)
        except:
            pass
        
        if result.returncode == 0:
            print("✅ Visual C++ Redistributable installed successfully")
            return True
        else:
            print("⚠️  Visual C++ Redistributable installation may have failed")
            print("Please try installing it manually from:")
            print(f"  {url}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to install Visual C++ Redistributable: {e}")
        print("Please install it manually from:")
        print(f"  {url}")
        return False

def uninstall_pytorch():
    """Uninstall current PyTorch installation"""
    print("\n🗑️  Uninstalling current PyTorch...")
    
    pytorch_packages = [
        'torch',
        'torchaudio',
        'torchvision',
        'torchtext'
    ]
    
    for package in pytorch_packages:
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'uninstall', package, '-y'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ Uninstalled {package}")
            else:
                print(f"  ⚠️  {package} not found or already uninstalled")
        except Exception as e:
            print(f"  ❌ Error uninstalling {package}: {e}")

def install_pytorch_cpu():
    """Install CPU-only PyTorch for better Windows compatibility"""
    print("\n📦 Installing PyTorch (CPU version for Windows compatibility)...")
    
    # Use CPU-only PyTorch for better Windows compatibility
    pytorch_install_cmd = [
        sys.executable, '-m', 'pip', 'install', 
        'torch==2.4.0+cpu', 
        'torchaudio==2.4.0+cpu',
        '--index-url', 'https://download.pytorch.org/whl/cpu'
    ]
    
    try:
        result = subprocess.run(pytorch_install_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ PyTorch (CPU) installed successfully")
            return True
        else:
            print(f"❌ PyTorch installation failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing PyTorch: {e}")
        return False

def test_pytorch_import():
    """Test if PyTorch can be imported"""
    print("\n🧪 Testing PyTorch import...")
    
    try:
        import torch
        import torchaudio
        print("✅ PyTorch imported successfully")
        print(f"  PyTorch version: {torch.__version__}")
        print(f"  TorchAudio version: {torchaudio.__version__}")
        
        # Test basic functionality
        x = torch.randn(2, 3)
        print(f"  ✅ Basic tensor operations work")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch import failed: {e}")
        return False

def install_alternative_requirements():
    """Install requirements with CPU-only PyTorch"""
    print("\n📦 Installing other requirements...")
    
    # Install other packages
    other_packages = [
        'tokenizers==0.21.0',
        'transformers==4.49.0',
        'huggingface_hub==0.28.1',
        'openai-whisper'
    ]
    
    for package in other_packages:
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ Installed {package}")
            else:
                print(f"  ❌ Failed to install {package}: {result.stderr}")
        except Exception as e:
            print(f"  ❌ Error installing {package}: {e}")

def create_cpu_only_process_audio():
    """Create a CPU-only version of process_audio.py for testing"""
    print("\n📝 Creating CPU-only test version...")
    
    cpu_script_content = '''import sys
import os
import traceback
import tempfile
import torch
import whisper
import torchaudio

def main(audio_path, output_path):
    try:
        print(f"Processing audio file: {audio_path}")
        print(f"Output will be saved to: {output_path}")
        
        # Check if input file exists
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Input audio file not found: {audio_path}")
        
        # Force CPU device for Windows compatibility
        device = "cpu"
        print(f"Using device: {device}")
        
        # Load Whisper model
        print("Loading Whisper model...")
        stt_model = whisper.load_model("tiny")  # Use tiny model for faster CPU processing
        
        # Transcribe audio
        print("Transcribing audio...")
        result = stt_model.transcribe(audio_path)
        user_text = result['text'].strip()
        print(f"User said: {user_text}")
        
        if not user_text:
            user_text = "I didn't catch that. Could you please repeat?"
        
        # Generate AI response
        ai_response = f"You said: {user_text}. That's interesting!"
        print(f"AI response: {ai_response}")

        # For now, create a simple beep sound as response
        # This is a fallback until CSM model works properly
        print("Generating simple audio response...")
        sample_rate = 16000
        duration = 2.0  # 2 seconds
        frequency = 440  # A4 note
        
        t = torch.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * torch.sin(2 * torch.pi * frequency * t)
        
        # Save the response audio
        print(f"Saving audio to: {output_path}")
        torchaudio.save(output_path, audio.unsqueeze(0), sample_rate)
        
        if os.path.exists(output_path):
            print(f"Successfully saved response audio: {output_path}")
        else:
            raise RuntimeError("Failed to save response audio file")
            
    except Exception as e:
        print(f"Error in process_audio.py: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python process_audio_cpu.py <input_audio_path> [output_audio_path]")
        sys.exit(1)
        
    audio_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "response.wav"
    main(audio_path, output_path)
'''
    
    with open('process_audio_cpu.py', 'w') as f:
        f.write(cpu_script_content)
    
    print("✅ Created process_audio_cpu.py (CPU-only version)")

def main():
    """Main fix function"""
    print("🔧 Windows PyTorch Fix Script")
    print("=" * 50)
    
    if not check_windows_version():
        sys.exit(1)
    
    print("\nThis script will:")
    print("1. Install Visual C++ Redistributable")
    print("2. Reinstall PyTorch (CPU version)")
    print("3. Test the installation")
    print("4. Create a CPU-only fallback version")
    
    response = input("\nProceed? (y/n): ").lower().strip()
    if response != 'y':
        print("Cancelled by user.")
        sys.exit(0)
    
    # Step 1: Install Visual C++ Redistributable
    install_visual_cpp_redistributable()
    
    # Step 2: Uninstall and reinstall PyTorch
    uninstall_pytorch()
    
    if not install_pytorch_cpu():
        print("\n❌ PyTorch installation failed. Please try manual installation:")
        print("pip install torch==2.4.0+cpu torchaudio==2.4.0+cpu --index-url https://download.pytorch.org/whl/cpu")
        sys.exit(1)
    
    # Step 3: Install other requirements
    install_alternative_requirements()
    
    # Step 4: Test PyTorch
    if test_pytorch_import():
        print("\n🎉 PyTorch is working! You can now try running the voice assistant.")
    else:
        print("\n⚠️  PyTorch still has issues. Creating CPU-only fallback...")
        create_cpu_only_process_audio()
        print("\nYou can test with: python process_audio_cpu.py input.wav output.wav")
    
    print("\n" + "=" * 50)
    print("Next steps:")
    print("1. Restart your command prompt/terminal")
    print("2. Run: python setup_and_run.py")
    print("3. If issues persist, try the CPU-only version")

if __name__ == "__main__":
    main()
'''
